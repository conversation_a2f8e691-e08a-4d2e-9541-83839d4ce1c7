import json
import re
from .agent_base import AgentBase
from utils.logger import LOG

class DocumentGeneratorAgent(AgentBase):
    """
    法律文书生成智能体
    支持生成起诉状、离婚协议书、劳动仲裁申请书等法律文书
    """
    
    def __init__(self, session_id=None):
        super().__init__(
            name="DocumentGenerator",
            prompt_file="prompts/document_generation_prompt.txt",
            session_id=session_id
        )
        
        # 文书类型配置
        self.document_types = {
            'lawsuit': {
                'name': '起诉状',
                'description': '民事起诉状、行政起诉状等诉讼文书',
                'icon': 'fas fa-gavel',
                'fields': [
                    {'name': 'plaintiff_name', 'label': '原告姓名', 'type': 'text', 'required': True},
                    {'name': 'plaintiff_gender', 'label': '原告性别', 'type': 'select', 'options': ['男', '女'], 'required': True},
                    {'name': 'plaintiff_age', 'label': '原告年龄', 'type': 'number', 'required': True},
                    {'name': 'plaintiff_nationality', 'label': '原告民族', 'type': 'text', 'required': True, 'default': '汉族'},
                    {'name': 'plaintiff_occupation', 'label': '原告职业', 'type': 'text', 'required': True},
                    {'name': 'plaintiff_address', 'label': '原告住址', 'type': 'textarea', 'required': True},
                    {'name': 'plaintiff_phone', 'label': '原告联系电话', 'type': 'tel', 'required': True},
                    {'name': 'defendant_name', 'label': '被告姓名', 'type': 'text', 'required': True},
                    {'name': 'defendant_gender', 'label': '被告性别', 'type': 'select', 'options': ['男', '女'], 'required': True},
                    {'name': 'defendant_age', 'label': '被告年龄', 'type': 'number', 'required': True},
                    {'name': 'defendant_nationality', 'label': '被告民族', 'type': 'text', 'required': True, 'default': '汉族'},
                    {'name': 'defendant_occupation', 'label': '被告职业', 'type': 'text', 'required': True},
                    {'name': 'defendant_address', 'label': '被告住址', 'type': 'textarea', 'required': True},
                    {'name': 'defendant_phone', 'label': '被告联系电话', 'type': 'tel', 'required': False},
                    {'name': 'lawsuit_requests', 'label': '诉讼请求', 'type': 'textarea', 'required': True, 'placeholder': '请详细描述您的诉讼请求'},
                    {'name': 'case_facts', 'label': '案件事实与理由', 'type': 'textarea', 'required': True, 'placeholder': '请详细描述案件的事实经过和法律依据'},
                    {'name': 'evidence_list', 'label': '证据清单', 'type': 'textarea', 'required': True, 'placeholder': '请列出相关证据'},
                    {'name': 'court_name', 'label': '管辖法院', 'type': 'text', 'required': True}
                ]
            },
            'divorce_agreement': {
                'name': '离婚协议书',
                'description': '协议离婚相关文书',
                'icon': 'fas fa-heart-broken',
                'fields': [
                    {'name': 'husband_name', 'label': '男方姓名', 'type': 'text', 'required': True},
                    {'name': 'husband_id', 'label': '男方身份证号', 'type': 'text', 'required': True},
                    {'name': 'husband_address', 'label': '男方住址', 'type': 'textarea', 'required': True},
                    {'name': 'wife_name', 'label': '女方姓名', 'type': 'text', 'required': True},
                    {'name': 'wife_id', 'label': '女方身份证号', 'type': 'text', 'required': True},
                    {'name': 'wife_address', 'label': '女方住址', 'type': 'textarea', 'required': True},
                    {'name': 'marriage_date', 'label': '结婚日期', 'type': 'date', 'required': True},
                    {'name': 'marriage_office', 'label': '结婚登记机关', 'type': 'text', 'required': True},
                    {'name': 'divorce_reason', 'label': '离婚原因', 'type': 'textarea', 'required': True},
                    {'name': 'children_arrangement', 'label': '子女抚养安排', 'type': 'textarea', 'required': False, 'placeholder': '如无子女可留空'},
                    {'name': 'property_division', 'label': '财产分割方案', 'type': 'textarea', 'required': True},
                    {'name': 'debt_arrangement', 'label': '债务承担约定', 'type': 'textarea', 'required': False, 'placeholder': '如无债务可留空'},
                    {'name': 'other_agreements', 'label': '其他约定事项', 'type': 'textarea', 'required': False}
                ]
            },
            'labor_arbitration': {
                'name': '劳动仲裁申请书',
                'description': '劳动争议仲裁申请',
                'icon': 'fas fa-hard-hat',
                'fields': [
                    {'name': 'applicant_name', 'label': '申请人姓名', 'type': 'text', 'required': True},
                    {'name': 'applicant_gender', 'label': '申请人性别', 'type': 'select', 'options': ['男', '女'], 'required': True},
                    {'name': 'applicant_age', 'label': '申请人年龄', 'type': 'number', 'required': True},
                    {'name': 'applicant_address', 'label': '申请人住址', 'type': 'textarea', 'required': True},
                    {'name': 'applicant_phone', 'label': '申请人联系电话', 'type': 'tel', 'required': True},
                    {'name': 'company_name', 'label': '被申请人单位名称', 'type': 'text', 'required': True},
                    {'name': 'company_address', 'label': '被申请人住所地', 'type': 'textarea', 'required': True},
                    {'name': 'legal_representative', 'label': '法定代表人', 'type': 'text', 'required': True},
                    {'name': 'company_phone', 'label': '被申请人联系电话', 'type': 'tel', 'required': False},
                    {'name': 'arbitration_requests', 'label': '仲裁请求', 'type': 'textarea', 'required': True, 'placeholder': '请详细描述您的仲裁请求'},
                    {'name': 'dispute_facts', 'label': '争议事实与理由', 'type': 'textarea', 'required': True, 'placeholder': '请详细描述劳动争议的事实经过和法律依据'},
                    {'name': 'evidence_list', 'label': '证据清单', 'type': 'textarea', 'required': True, 'placeholder': '请列出相关证据'},
                    {'name': 'arbitration_committee', 'label': '仲裁委员会名称', 'type': 'text', 'required': True}
                ]
            }
        }
    
    def get_document_types(self):
        """获取支持的文书类型列表"""
        return [
            {
                'id': doc_id,
                'name': doc_info['name'],
                'description': doc_info['description'],
                'icon': doc_info['icon']
            }
            for doc_id, doc_info in self.document_types.items()
        ]
    
    def get_document_fields(self, document_type):
        """获取指定文书类型的表单字段"""
        if document_type not in self.document_types:
            raise ValueError(f"不支持的文书类型: {document_type}")
        
        return self.document_types[document_type]['fields']
    
    def generate_document(self, document_type, form_data, session_id=None):
        """
        生成法律文书
        
        参数:
            document_type (str): 文书类型
            form_data (dict): 表单数据
            session_id (str): 会话ID
        
        返回:
            str: 生成的法律文书内容
        """
        if document_type not in self.document_types:
            raise ValueError(f"不支持的文书类型: {document_type}")
        
        # 构建生成请求
        doc_info = self.document_types[document_type]
        request_text = f"请根据以下信息生成{doc_info['name']}：\n\n"
        
        # 添加表单数据
        for field in doc_info['fields']:
            field_name = field['name']
            field_label = field['label']
            field_value = form_data.get(field_name, '')
            
            if field_value:
                request_text += f"{field_label}：{field_value}\n"
        
        request_text += "\n请严格按照法律文书的标准格式生成完整的文书内容。"
        
        # 调用AI生成文书
        try:
            document_content = self.chat_with_history(request_text, session_id)
            LOG.info(f"成功生成{doc_info['name']}")
            return document_content
        except Exception as e:
            LOG.error(f"生成{doc_info['name']}失败: {str(e)}")
            raise e
    
    def validate_form_data(self, document_type, form_data):
        """
        验证表单数据
        
        参数:
            document_type (str): 文书类型
            form_data (dict): 表单数据
        
        返回:
            tuple: (is_valid, error_messages)
        """
        if document_type not in self.document_types:
            return False, ["不支持的文书类型"]
        
        errors = []
        doc_info = self.document_types[document_type]
        
        for field in doc_info['fields']:
            field_name = field['name']
            field_label = field['label']
            field_value = form_data.get(field_name, '')
            
            # 检查必填字段
            if field.get('required', False) and not field_value:
                errors.append(f"{field_label}不能为空")
            
            # 检查字段类型
            if field_value:
                field_type = field.get('type', 'text')
                
                if field_type == 'number':
                    try:
                        int(field_value)
                    except ValueError:
                        errors.append(f"{field_label}必须是数字")
                
                elif field_type == 'tel':
                    # 简单的电话号码验证
                    if not re.match(r'^1[3-9]\d{9}$', field_value.replace('-', '').replace(' ', '')):
                        errors.append(f"{field_label}格式不正确")
        
        return len(errors) == 0, errors
